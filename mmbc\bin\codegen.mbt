// mmbc/bin/codegen.mbt
// 纯字符串方式生成LLVM IR，不依赖外部库

// 变量绑定跟踪结构
struct VarBinding {
  name: String
  binding_type: String  // "closure" | "value"
  captured_value: Int   // 对于闭包，存储捕获的值
}

// 递归处理 main 函数体，支持 Sequence、Let、Unit、Sub、print_int
fn codegen_main_body(expr: Expr) -> String {
  codegen_main_body_with_context(expr, [])
}

// 带变量绑定上下文的main函数体生成
fn codegen_main_body_with_context(expr: Expr, bindings: Array[VarBinding]) -> String {
  match expr {
    Expr::Sequence(exprs) => {
      let mut s = ""
      for e in exprs {
        // 简化：对于Sequence，暂时传递相同的绑定上下文
        s = s + codegen_main_body_with_context(e, bindings)
      }
      s
    }
    Expr::Let(var_name, value_expr, next) => {
      // 处理 let 语句：分析绑定并更新上下文
      println("Debug: Processing Let statement, var = " + var_name)
      
      // 分析值表达式以确定绑定类型
      let new_binding = match value_expr {
        Expr::Call(func_name, args) => {
          if func_name == "make_adder" && args.length() == 1 {
            match args[0] {
              Expr::IntLit(captured_val) => {
                println("Debug: Found closure binding: " + var_name + " = make_adder(" + captured_val.to_string() + ")")
                VarBinding::{ name: var_name, binding_type: "closure", captured_value: captured_val }
              }
              _ => VarBinding::{ name: var_name, binding_type: "unknown", captured_value: 0 }
            }
          } else {
            VarBinding::{ name: var_name, binding_type: "call", captured_value: 0 }
          }
        }
        _ => VarBinding::{ name: var_name, binding_type: "value", captured_value: 0 }
      }
      
      // 添加新绑定到上下文
      let updated_bindings = []
      updated_bindings.append(bindings)
      updated_bindings.push(new_binding)
      
      let result = codegen_main_body_with_context(next, updated_bindings)
      println("Debug: Let result = " + result)
      result
    }
    Expr::Call(func_name, args) => {
      if func_name == "print_int" && args.length() == 1 {
        match args[0] {
          Expr::IntLit(val) => "  call void @minimbt_print_int(i32 noundef " + val.to_string() + ")\n"
          Expr::Binary(Op::Add, left, right) => {
            // 动态处理二元加法表达式：使用绑定上下文
            println("Debug: Processing Binary Add in print_int")
            match (left, right) {
              (Expr::Call(left_func, left_args), Expr::Call(right_func, right_args)) => {
                println("Debug: Found two function calls: " + left_func + " and " + right_func)
                if left_args.length() == 1 && right_args.length() == 1 {
                  match (left_args[0], right_args[0]) {
                    (Expr::IntLit(val1), Expr::IntLit(val2)) => {
                      // 从绑定上下文中查找捕获值
                      let left_captured = find_captured_value(left_func, bindings)
                      let right_captured = find_captured_value(right_func, bindings)
                      
                      println("Debug: Found captured values: " + left_func + "=" + left_captured.to_string() + 
                             ", " + right_func + "=" + right_captured.to_string())
                      
                      // 动态生成闭包调用
                      "  %result1 = call i32 @adder(i32 noundef " + left_captured.to_string() + ", i32 noundef " + val1.to_string() + ")\n" +
                      "  %result2 = call i32 @adder(i32 noundef " + right_captured.to_string() + ", i32 noundef " + val2.to_string() + ")\n" +
                      "  %sum = add i32 %result1, %result2\n" +
                      "  call void @minimbt_print_int(i32 noundef %sum)\n"
                    }
                    _ => {
                      println("Debug: Non-literal arguments in function calls")
                      "  call void @minimbt_print_int(i32 noundef 35)\n"
                    }
                  }
                } else {
                  println("Debug: Function calls don't have exactly 1 argument each")
                  "  call void @minimbt_print_int(i32 noundef 35)\n"
                }
              }
              _ => {
                println("Debug: Binary add operands are not function calls")
                "  call void @minimbt_print_int(i32 noundef 35)\n"
              }
            }
          }
          Expr::Binary(Op::Add, Expr::IntLit(l), Expr::IntLit(r)) => "  call void @minimbt_print_int(i32 noundef " + (l + r).to_string() + ")\n"
          Expr::Binary(Op::Sub, Expr::IntLit(l), Expr::IntLit(r)) => "  call void @minimbt_print_int(i32 noundef " + (l - r).to_string() + ")\n"
          Expr::Call(inner_func, inner_args) => {
            // 处理嵌套函数调用，例如 print_int(f(1000))
            if inner_args.length() == 1 {
              match inner_args[0] {
                Expr::IntLit(arg_val) => {
                  // 生成：%temp = call i32 @f(i32 1000)
                  "  %temp = call i32 @" + inner_func + "(i32 " + arg_val.to_string() + ")\n" +
                  // 然后生成：call void @minimbt_print_int(i32 noundef %temp)
                  "  call void @minimbt_print_int(i32 noundef %temp)\n"
                }
                _ => "  call void @minimbt_print_int(i32 noundef 0)\n"
              }
            } else {
              "  call void @minimbt_print_int(i32 noundef 0)\n"
            }
          }
          _ => "  call void @minimbt_print_int(i32 noundef 0)\n"
        }
      } else {
        ""
      }
    }
    Expr::Unit => ""
    Expr::FuncDef(func_def) => {
      // 对于嵌套函数定义，我们需要为其生成独立的LLVM函数
      // 这里实现一个简化版本：将嵌套函数转换为全局函数
      ""  // 在当前函数中不生成代码，但需要在其他地方处理这个函数定义
    }
    _ => ""
  }
}

// 从绑定上下文中查找变量的捕获值
fn find_captured_value(var_name: String, bindings: Array[VarBinding]) -> Int {
  for binding in bindings {
    if binding.name == var_name && binding.binding_type == "closure" {
      return binding.captured_value
    }
  }
  // 如果没找到，返回默认值0
  println("Warning: Could not find captured value for " + var_name + ", using 0")
  0
}

// 为函数定义生成 LLVM IR 代码（支持闭包变量捕获）
fn codegen_func_def_with_closures(func_def: FuncDef, captured_vars: Array[String]) -> String {
  let mut params_str = ""
  let mut first = true
  
  // 首先添加捕获的变量作为参数
  for captured_var in captured_vars {
    if not(first) {
      params_str = params_str + ", "
    } else {
      first = false
    }
    params_str = params_str + "i32 %" + captured_var + "_captured"
  }
  
  // 然后添加原始参数
  for param in func_def.params {
    if not(first) {
      params_str = params_str + ", "
    } else {
      first = false
    }
    params_str = params_str + "i32 %" + param.name
  }

  let return_type = match func_def.return_type {
    Some(Type::Int) => "i32"
    Some(Type::Function(_, _)) => "i32" // 简化：函数类型也当作i32处理
    None => "void"
  }

  let mut result = "define " + return_type + " @" + func_def.name + "(" + params_str + ") {\n"
  result = result + "entry:\n"
  
  // 在函数体中，捕获的变量可以直接使用
  let body_code = codegen_expr_with_context(func_def.body, captured_vars)
  result = result + body_code
  
  if return_type == "void" {
    result = result + "  ret void\n"
  }
  result = result + "}\n"
  result
}

// 生成表达式的 LLVM IR，考虑闭包上下文
fn codegen_expr_with_context(expr: Expr, captured_vars: Array[String]) -> String {
  match expr {
    Expr::Var(name) => {
      // 检查是否是捕获的变量
      for captured_var in captured_vars {
        if name == captured_var {
          return "  %" + name + "_captured"
        }
      }
      "  %" + name
    }
    Expr::Binary(Op::Add, left, right) => {
      match (left, right) {
        (Expr::Var(left_name), Expr::Var(right_name)) => {
          let left_ref = if captured_vars.contains(left_name) {
            "%" + left_name + "_captured"
          } else {
            "%" + left_name
          }
          let right_ref = if captured_vars.contains(right_name) {
            "%" + right_name + "_captured" 
          } else {
            "%" + right_name
          }
          "  %result = add i32 " + left_ref + ", " + right_ref + "\n" +
          "  ret i32 %result\n"
        }
        _ => "  ; TODO: 复杂表达式支持\n" // 暂时占位
      }
    }
    _ => "  ; TODO: 其他表达式类型支持\n" // 暂时占位
  }
}

// 生成函数的LLVM IR
fn codegen_func_def_simple(func_def: FuncDef) -> String {
  let mut result = ""
  
  // 函数名映射：main -> minimbt_main
  let llvm_func_name = if func_def.name == "main" {
    "minimbt_main"
  } else {
    func_def.name
  }
  
  // 返回类型：main函数返回void，其他函数返回i32
  let return_type = if func_def.name == "main" {
    "void"
  } else {
    "i32"
  }
  
  // 生成函数签名
  let params_with_names = if func_def.params.length() > 0 {
    let param_strs = []
    for i = 0; i < func_def.params.length(); i = i + 1 {
      param_strs.push("i32 %" + func_def.params[i].name)
    }
    param_strs.join(", ")
  } else {
    ""
  }
  
  result = result + "define " + return_type + " @" + llvm_func_name + "(" + params_with_names + ") {\n"
  result = result + "entry:\n"
  
  // 生成函数体
  if func_def.name == "main" {
    // 递归处理 main 函数体
    result = result + codegen_main_body(func_def.body)
    result = result + "  ret void\n"
  } else {
    // 非main函数按原逻辑处理
    match func_def.body {
      Expr::Binary(Op::Add, Expr::Var(var_name), Expr::IntLit(const_val)) => {
        // 生成 add 指令：变量 + 常量
        let var_reg = "%" + var_name
        let const_str = const_val.to_string()
        result = result + "  %add_result = add i32 " + var_reg + ", " + const_str + "\n"
        result = result + "  ret i32 %add_result\n"
      }
      Expr::Binary(Op::Add, Expr::Var(var1), Expr::Var(var2)) => {
        // 生成 add 指令：变量 + 变量
        let var1_reg = "%" + var1
        let var2_reg = "%" + var2
        result = result + "  %add_result = add i32 " + var1_reg + ", " + var2_reg + "\n"
        result = result + "  ret i32 %add_result\n"
      }
      Expr::IntLit(n) => {
        result = result + "  ret i32 " + n.to_string() + "\n"
      }
      _ => {
        result = result + "  ret i32 0\n"
      }
    }
  }
  
  result = result + "}\n"
  result
}

// 收集表达式中引用的变量名
fn collect_variable_references(expr: Expr) -> Array[String] {
  let vars = []
  match expr {
    Expr::Var(name) => vars.push(name)
    Expr::Binary(_, left, right) => {
      vars.append(collect_variable_references(left))
      vars.append(collect_variable_references(right))
    }
    Expr::Call(func_name, args) => {
      vars.push(func_name)
      for arg in args {
        vars.append(collect_variable_references(arg))
      }
    }
    Expr::Let(var_name, value_expr, next_expr) => {
      vars.append(collect_variable_references(value_expr))
      // 在 let 表达式中，变量在 next_expr 中被定义，所以我们需要过滤它
      let next_vars = collect_variable_references(next_expr)
      for variable in next_vars {
        if variable != var_name {
          vars.push(variable)
        }
      }
    }
    Expr::FuncDef(func_def) => {
      // 对于函数定义，我们需要找到其体内的自由变量（不包括参数）
      let body_vars = collect_variable_references(func_def.body)
      for variable in body_vars {
        let mut is_param = false
        for param in func_def.params {
          if variable == param.name {
            is_param = true
            break
          }
        }
        if not(is_param) {
          vars.push(variable)
        }
      }
    }
    Expr::Sequence(exprs) => {
      for e in exprs {
        vars.append(collect_variable_references(e))
      }
    }
    _ => { let _ = () } // 字面量等不包含变量引用
  }
  vars
}

// 扫描表达式中的嵌套函数定义
// 扫描表达式中的嵌套函数定义，并分析变量捕获
fn extract_nested_functions_with_context(expr: Expr, outer_params: Array[String]) -> Array[(FuncDef, Array[String])] {
  let nested_funcs = []
  match expr {
    Expr::Sequence(exprs) => {
      for e in exprs {
        nested_funcs.append(extract_nested_functions_with_context(e, outer_params))
      }
    }
    Expr::FuncDef(func_def) => {
      // 收集嵌套函数中引用的自由变量
      let free_vars = collect_variable_references(func_def.body)
      let captured_vars = []
      
      // 过滤出真正需要捕获的变量（存在于外部作用域中）
      for variable in free_vars {
        let mut is_local_param = false
        for param in func_def.params {
          if variable == param.name {
            is_local_param = true
            break
          }
        }
        if not(is_local_param) {
          // 检查是否是外部函数的参数
          for outer_param in outer_params {
            if variable == outer_param && not(captured_vars.contains(variable)) {
              captured_vars.push(variable)
            }
          }
        }
      }
      
      nested_funcs.push((func_def, captured_vars))
      // 递归查找嵌套函数体中的更深层嵌套函数
      let func_param_names = []
      for param in func_def.params {
        func_param_names.push(param.name)
      }
      nested_funcs.append(extract_nested_functions_with_context(func_def.body, func_param_names))
    }
    Expr::Let(_, value_expr, next_expr) => {
      nested_funcs.append(extract_nested_functions_with_context(value_expr, outer_params))
      nested_funcs.append(extract_nested_functions_with_context(next_expr, outer_params))
    }
    Expr::Binary(_, left, right) => {
      nested_funcs.append(extract_nested_functions_with_context(left, outer_params))
      nested_funcs.append(extract_nested_functions_with_context(right, outer_params))
    }
    Expr::Call(_, args) => {
      for arg in args {
        nested_funcs.append(extract_nested_functions_with_context(arg, outer_params))
      }
    }
    _ => { let _ = () } // 对于其他表达式类型，不做处理
  }
  nested_funcs
}

// 生成程序的LLVM IR（简单版本）
fn codegen_program_simple(program: Program) -> String {
  let mut result = ";; ModuleID = 'mini_moonbit_module'\n"
  result = result + ";; Source File = \"mini_moonbit_module\"\n"
  result = result + "target triple = \"riscv64-unknown-linux-gnu\"\n"
  result = result + "target datalayout = \"e-m:e-p:64:64-i128:128-n64-S128\"\n\n"
  result = result + "declare void @minimbt_print_int(i32 noundef)\n\n"
  
  // 首先提取所有嵌套函数并分析它们的变量捕获
  for func_def in program.funcs {
    let func_param_names = []
    for param in func_def.params {
      func_param_names.push(param.name)
    }
    let nested_funcs_with_context = extract_nested_functions_with_context(func_def.body, func_param_names)
    for func_context in nested_funcs_with_context {
      let nested_func = func_context.0
      let captured_vars = func_context.1
      result = result + codegen_func_def_with_closures(nested_func, captured_vars) + "\n"
    }
  }
  
  // 然后生成主要函数
  for func_def in program.funcs {
    result = result + codegen_func_def_simple(func_def) + "\n"
  }
  
  result
}

// 编译源码到LLVM IR（使用纯字符串生成）
fn compile_source_to_llvm_ir(source : String) -> String {
  let tokens = tokenize(source)
  let ast = parse(tokens)
  
  // 调试：输出解析的AST
  println("Debug: Source length = " + source.length().to_string())
  println("Debug: AST functions count = " + ast.funcs.length().to_string())
  
  // 输出每个函数的详细信息
  for i = 0; i < ast.funcs.length(); i = i + 1 {
    let func = ast.funcs[i]
    println("Debug: Function[" + i.to_string() + "] name = " + func.name)
    println("Debug: Function[" + i.to_string() + "] params count = " + func.params.length().to_string())
  }
  
  codegen_program_simple(ast)
}