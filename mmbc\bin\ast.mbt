// mmbc/bin/ast.mbt
// 定义我们编译器前端所需的抽象语法树（AST）数据结构

// 运算符定义
pub enum Op {
  Add // 加法
  Sub // 减法
}

// 类型定义
pub enum Type {
  Int
  Function(Array[Type], Type) // 函数类型：参数类型数组 -> 返回类型
}

// 表达式（Expression）的定义
// 一个表达式最终会产生一个值
pub enum Expr {
  IntLit(Int) // 整数常量, 例如: 1, 42
  Binary(Op, Expr, Expr) // 二元运算, 例如: expr + expr
  Var(String) // 变量引用, 例如: x
  Call(String, Array[Expr]) // 函数调用, 例如: f(1, 2)
  Let(String, Expr, Expr) // let绑定: let var = expr1; expr2
  Sequence(Array[Expr]) // 表达式序列
  Unit // 单元类型 ()
  FuncDef(FuncDef) // 嵌套函数定义
}

// 为Expr实现to_string方法用于调试
pub fn Expr::to_string(self: Self) -> String {
  match self {
    IntLit(n) => n.to_string()
    Binary(Op::Add, left, right) => "(" + left.to_string() + " + " + right.to_string() + ")"
    Binary(Op::Sub, left, right) => "(" + left.to_string() + " - " + right.to_string() + ")"
    Var(name) => name
    Call(func_name, args) => {
      let mut args_str = ""
      for i = 0; i < args.length(); i = i + 1 {
        if i > 0 { args_str = args_str + ", " }
        args_str = args_str + args[i].to_string()
      }
      func_name + "(" + args_str + ")"
    }
    Let(var, expr1, expr2) => "let " + var + " = " + expr1.to_string() + "; " + expr2.to_string()
    Sequence(exprs) => {
      let mut result = "{ "
      for i = 0; i < exprs.length(); i = i + 1 {
        if i > 0 { result = result + "; " }
        result = result + exprs[i].to_string()
      }
      result + " }"
    }
    Unit => "()"
    FuncDef(func_def) => "fn " + func_def.name + "(...) { ... }"
  }
}

// 函数参数
pub struct Param {
  name: String
  param_type: Type
}

// 函数定义
pub struct FuncDef {
  name: String // 函数名, 例如: "main"
  params: Array[Param] // 函数参数
  return_type: Option[Type] // 函数返回类型, None表示无返回类型
  body: Expr // 函数体, 暂时简化为一个表达式
}

// 顶层结构：一个程序可以看作是多个函数定义的集合
pub struct Program {
  funcs: Array[FuncDef]
}
