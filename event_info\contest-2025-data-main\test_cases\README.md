# Public test cases

- `mbt/` stores the source files of test cases
- `ans/` stores the expected output
- `func.c` is the reference implementation of external functions


## Base language tests

- ack.mbt
- adder.mbt
- adder2.mbt
- caltz.mbt
- clamp.mbt
- cls-bug.mbt
- cls-bug2.mbt
- cls-rec.mbt
- cls-reg-bug.mbt
- counter.mbt
- even-odd.mbt
- float.mbt
- funcomp.mbt
- gcd.mbt
- id.mbt
- inprod-loop.mbt
- inprod-rec.mbt
- inprod.mbt
- join-reg.mbt
- join-reg2.mbt
- join-stack.mbt
- join-stack2.mbt
- join-stack3.mbt
- matmul-flat.mbt
- matmul-loop.mbt
- matmul.mbt
- non-tail-if.mbt
- non-tail-if2.mbt
- print.mbt
- shuffle.mbt
- spill.mbt
- spill2.mbt
- spill3.mbt
- sum-tail.mbt
- sum.mbt

## Test cases with optional language features

- conv_pool.mbt
- enum1.mbt
- enum2.mbt
- generic1.mbt
- generic2.mbt
- struct1.mbt
- struct2.mbt
