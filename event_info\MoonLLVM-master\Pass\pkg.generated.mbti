// Generated using `moon info`, DON'T EDIT IT
package "Kaida-Amethyst/MoonLLVM/Pass"

import(
  "Kaida-Amethyst/MoonLLVM/IR"
)

// Values

// Errors

// Types and methods
pub struct DCE {
}
impl FunctionPass for DCE

pub(all) enum OptLevel {
  Empty
  O0
}

pub struct PassManager {
  function_passes : Array[&FunctionPass]
}
fn PassManager::addPass(Self, &FunctionPass) -> Unit
fn PassManager::create(OptLevel) -> Self
fn PassManager::createEmpty() -> Self
fn PassManager::createO0() -> Self
fn PassManager::run(Self, @IR.Function) -> Unit

impl Eq for &FunctionPass

// Type aliases

// Traits
pub trait FunctionPass {
  name(Self) -> String
  description(Self) -> String
  run(Self, @IR.Function) -> Unit
}

