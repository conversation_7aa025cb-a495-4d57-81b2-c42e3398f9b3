///|
fn[T] unless(cond : <PERSON><PERSON>, value : () -> T) -> T? {
  if !cond {
    Some(value())
  } else {
    None
  }
}

///|
fn[T] when(cond : <PERSON>ol, value : () -> T) -> T? {
  if cond {
    Some(value())
  } else {
    None
  }
}

///|
fn isInValidName(name : String) -> Bool {
  if name.is_empty() {
    return true
  }
  let contain_illegal_chars = !name
    .iter()
    .all(c => c.is_ascii_alphabetic() || c.is_ascii_digit() || c == '_')
  contain_illegal_chars
}
