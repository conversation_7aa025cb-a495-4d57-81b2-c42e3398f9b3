// mmbc/bin/lexer.mbt

pub enum Token {
  Fn
  Let         // let
  Ident(String)
  IntLit(Int)
  Plus
  Minus       // -
  LParen
  RParen
  LBrace
  RBrace
  Colon       // :
  Arrow       // ->
  Comma       // ,
  Assign      // =
  Semicolon   // ;
  Eof
} derive(Show, Eq)

fn tokenize(source : String) -> Array[Token] {
  println("Debug: Lexer - Starting tokenization, source length: " + source.length().to_string())
  let tokens : Array[Token] = []
  let mut pos = 0
  let chars = source.to_bytes()
  
  while pos < chars.length() {
    // Skip UTF-16 null bytes (every other byte)
    if pos % 2 == 1 {
      pos += 1
      continue
    }
    
    let byte = chars[pos]
    let char_code = byte.to_int()
    
    if char_code == 32 || char_code == 9 || char_code == 10 || char_code == 13 {
      // Whitespace (space, tab, newline, carriage return)
      pos += 2  // Skip both bytes in UTF-16
    } else if char_code == 102 {  // 'f'
      if pos + 2 < chars.length() && chars[pos + 2].to_int() == 110 {  // 'n'
        // Check if this is followed by whitespace or delimiter (not alphanumeric)
        if pos + 4 >= chars.length() || 
           chars[pos + 4].to_int() == 32 || chars[pos + 4].to_int() == 9 || 
           chars[pos + 4].to_int() == 10 || chars[pos + 4].to_int() == 13 {
          tokens.push(Fn)
          pos += 4  // Skip 'fn' (2 chars * 2 bytes each)
        } else {
          // This is an identifier starting with 'f'
          let mut ident = ""
          while pos < chars.length() {
            let cc = chars[pos].to_int()
            if (cc >= 65 && cc <= 90) || (cc >= 97 && cc <= 122) || (cc >= 48 && cc <= 57) || cc == 95 {
              ident += Int::unsafe_to_char(cc).to_string()
              pos += 2
            } else {
              break
            }
          }
          tokens.push(Ident(ident))
        }
      } else {
        // This is an identifier starting with 'f'
        let mut ident = ""
        while pos < chars.length() {
          let cc = chars[pos].to_int()
          if (cc >= 65 && cc <= 90) || (cc >= 97 && cc <= 122) || (cc >= 48 && cc <= 57) || cc == 95 {
            ident += Int::unsafe_to_char(cc).to_string()
            pos += 2
          } else {
            break
          }
        }
        tokens.push(Ident(ident))
      }
    } else if char_code == 40 {  // '('
      tokens.push(LParen)
      pos += 2
    } else if char_code == 41 {  // ')'
      tokens.push(RParen)
      pos += 2
    } else if char_code == 123 {  // '{'
      tokens.push(LBrace)
      pos += 2
    } else if char_code == 125 {  // '}'
      tokens.push(RBrace)
      pos += 2
    } else if char_code == 43 {  // '+'
      tokens.push(Plus)
      pos += 2
    } else if char_code == 58 {  // ':'
      tokens.push(Colon)
      pos += 2
    } else if char_code == 44 {  // ','
      tokens.push(Comma)
      pos += 2
    } else if char_code == 61 {  // '='
      tokens.push(Assign)
      pos += 2
    } else if char_code == 59 {  // ';'
      tokens.push(Semicolon)
      pos += 2
    } else if char_code == 45 {  // '-'
      if pos + 2 < chars.length() && chars[pos + 2].to_int() == 62 {  // '>'
        tokens.push(Arrow)
        pos += 4  // Skip '->' (2 chars * 2 bytes each)
      } else {
        tokens.push(Minus)
        pos += 2
      }
    } else if char_code >= 48 && char_code <= 57 {  // Digits '0'-'9'
      let mut num_str = ""
      while pos < chars.length() && chars[pos].to_int() >= 48 && chars[pos].to_int() <= 57 {
        num_str += Int::unsafe_to_char(chars[pos].to_int()).to_string()
        pos += 2  // Skip UTF-16 pair
      }
      let num = try {
        @strconv.parse_int(num_str)
      } catch {
        _ => {
          println("Failed to parse integer: " + num_str)
          panic()
        }
      }
      tokens.push(IntLit(num))
    } else if (char_code >= 65 && char_code <= 90) || (char_code >= 97 && char_code <= 122) || char_code == 95 {
      // Letters and underscore
      let mut ident = ""
      while pos < chars.length() {
        let cc = chars[pos].to_int()
        if (cc >= 65 && cc <= 90) || (cc >= 97 && cc <= 122) || (cc >= 48 && cc <= 57) || cc == 95 {
          ident += Int::unsafe_to_char(cc).to_string()
          pos += 2
        } else {
          break
        }
      }
      // 检查是否是关键字
      if ident == "let" {
        tokens.push(Let)
      } else {
        tokens.push(Ident(ident))
      }
    } else {
      println("Unknown character found in source: ASCII " + char_code.to_string())
      panic()
    }
  }
  
  tokens.push(Eof)
  println("Debug: Lexer - Finished tokenization, generated " + tokens.length().to_string() + " tokens")
  tokens
}
