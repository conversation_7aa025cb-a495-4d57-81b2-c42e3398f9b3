// mmbc/bin/parser.mbt
// 语法分析器 (Parser)

struct Parser {
  tokens: Array[Token]
  mut pos: Int
}

fn Parser::new(tokens: Array[Token]) -> Parser {
  { tokens: tokens, pos: 0 }
}

// --- 解析器的辅助函数 ---

fn Parser::peek(self: Self) -> Token {
  if self.pos >= self.tokens.length() {
    return Token::Eof
  }
  self.tokens[self.pos]
}

fn Parser::consume(self: Self) -> Token {
  let token = self.peek()
  if self.pos < self.tokens.length() {
    self.pos = self.pos + 1
  }
  token
}

fn Parser::expect(self: Self, expected_fn: (Token) -> Bool, message: String) -> Token {
  let token = self.peek()
  if expected_fn(token) {
    return self.consume()
  } else {
    println(message)
    panic()
  }
}

fn Parser::is_at_end(self: Self) -> <PERSON><PERSON> {
  self.pos >= self.tokens.length()
}

// 解析类型（支持函数类型）
fn Parser::parse_type(self: Self) -> Type {
  match self.peek() {
    Token::Ident("Int") => {
      let _ = self.consume()
      Type::Int
    }
    Token::LParen => {
      // 解析函数类型 (Int) -> Int
      let _ = self.consume() // 消费 '('
      let param_types = []
      
      // 解析参数类型列表
      while self.peek() != Token::RParen && !self.is_at_end() {
        param_types.push(self.parse_type())
        if self.peek() == Token::Comma {
          let _ = self.consume() // 消费 ','
        }
      }
      
      let _ = self.expect(fn(t) { match t { Token::RParen => true; _ => false } }, "Expected ')' in function type.")
      
      // 期望 ->
      let _ = self.expect(fn(t) { match t { Token::Arrow => true; _ => false } }, "Expected '->' in function type.")
      
      // 解析返回类型
      let return_type = self.parse_type()
      
      Type::Function(param_types, return_type)
    }
    _ => {
      println("Expected type.")
      panic()
    }
  }
}

// --- 解析逻辑 ---

/// 解析基础表达式（整数常量、变量、函数调用）
fn Parser::parse_primary(self: Self) -> Expr {
  let current_token = self.peek()
  match current_token {
    Token::IntLit(n) => {
      let _ = self.consume()
      Expr::IntLit(n)
    }
    Token::Minus => {
      let _ = self.consume()
      match self.consume() {
        Token::IntLit(n) => Expr::IntLit(-n)
        _ => {
          println("Expected integer after minus.")
          panic()
        }
      }
    }
    Token::Ident(name) => {
      let _ = self.consume()
      if self.peek() == Token::LParen {
        let _ = self.consume() // 消费 '('
        let args = []
        while self.peek() != Token::RParen && !self.is_at_end() {
          args.push(self.parse_expr())
          if self.peek() == Token::Comma {
            let _ = self.consume()
          }
        }
        let _ = self.expect(fn(t) { match t { Token::RParen => true; _ => false } }, "Expected ')'.")
        Expr::Call(name, args)
      } else {
        Expr::Var(name)
      }
    }
    Token::LParen => {
      let _ = self.consume()
      if self.peek() == Token::RParen {
        let _ = self.consume()
        Expr::Unit
      } else {
        let expr = self.parse_expr()
        let _ = self.expect(fn(t) { match t { Token::RParen => true; _ => false } }, "Expected ')'.")
        expr
      }
    }
    Token::Let => {
      println("Debug: Parser - Found Let token")
      let _ = self.consume()
      let var_name = match self.consume() {
        Token::Ident(s) => {
          println("Debug: Parser - Let variable name: " + s)
          s
        }
        _ => {
          println("Expected identifier after let.")
          panic()
        }
      }
      let _ = self.expect(fn(t) { match t { Token::Assign => true; _ => false } }, "Expected '=' after let var.")
      println("Debug: Parser - Parsing let value expression")
      let value_expr = self.parse_add_sub() // 只解析值表达式，不要递归parse_expr
      
      // 检查是否有分号，如果有就解析后续表达式
      if self.peek() == Token::Semicolon {
        println("Debug: Parser - Found semicolon after let value, parsing next expression")
        let _ = self.consume() // 消费分号
        let next_expr = self.parse_expr() // 递归解析后续表达式
        println("Debug: Parser - Let with next expression parsed")
        Expr::Let(var_name, value_expr, next_expr)
      } else {
        println("Debug: Parser - Let without semicolon, using Unit as next")
        Expr::Let(var_name, value_expr, Expr::Unit)
      }
    }
    Token::Fn => {
      // 解析嵌套函数定义
      let func_def = self.parse_func_def()
      Expr::FuncDef(func_def)
    }
    _ => {
      println("Expected a primary expression.")
      panic()
    }
  }
}

/// 解析表达式（目前支持加法）
fn Parser::parse_expr(self: Self) -> Expr {
  // 支持分号表达式序列
  let exprs = []
  exprs.push(self.parse_add_sub())
  while self.peek() == Token::Semicolon {
    let _ = self.consume()
    if self.peek() == Token::RBrace || self.peek() == Token::Eof {
      break
    }
    exprs.push(self.parse_add_sub())
  }
  if exprs.length() == 1 {
    exprs[0]
  } else {
    Expr::Sequence(exprs)
  }
}

// 新增：加法和减法表达式
fn Parser::parse_add_sub(self: Self) -> Expr {
  let mut lhs = self.parse_primary()
  while true {
    match self.peek() {
      Token::Plus => {
        let _ = self.consume()
        let rhs = self.parse_primary()
        lhs = Expr::Binary(Op::Add, lhs, rhs)
      }
      Token::Minus => {
        let _ = self.consume()
        let rhs = self.parse_primary()
        lhs = Expr::Binary(Op::Sub, lhs, rhs)
      }
      _ => break
    }
  }
  lhs
}

/// 解析一个函数定义
fn Parser::parse_func_def(self: Self) -> FuncDef {
  let _ = self.expect(fn(t) { match t { Token::Fn => true; _ => false } }, "Expected 'fn' keyword.")

  let name = match self.consume() {
    Token::Ident(s) => s
    _ => {
      println("Expected function name.")
      panic()
    }
  }

  // 检查是否有参数列表
  let (params, return_type) = if self.peek() == Token::LParen {
    let _ = self.expect(fn(t) { match t { Token::LParen => true; _ => false } }, "Expected '('.")
    
    // 解析参数列表
    let params = []
    while self.peek() != Token::RParen && !self.is_at_end() {
      let param_name = match self.consume() {
        Token::Ident(s) => s
        _ => {
          println("Expected parameter name.")
          panic()
        }
      }
      
      let _ = self.expect(fn(t) { match t { Token::Colon => true; _ => false } }, "Expected ':'.")
      
      let param_type = self.parse_type()
      
      params.push({ name: param_name, param_type: param_type })
      
      if self.peek() == Token::Comma {
        let _ = self.consume() // 消费 ','
      }
    }
    
    let _ = self.expect(fn(t) { match t { Token::RParen => true; _ => false } }, "Expected ')'.")
    
    // 解析返回类型（可选）
    let return_type = if self.peek() == Token::Arrow {
      let _ = self.consume() // 消费 '->'
      Some(self.parse_type())
    } else {
      None
    }
    
    (params, return_type)
  } else {
    // 没有参数列表的函数，如 fn main { ... }
    ([], None)
  }

  let _ = self.expect(fn(t) { match t { Token::LBrace => true; _ => false } }, "Expected '{'.")

  let body_expr = self.parse_expr()

  let _ = self.expect(fn(t) { match t { Token::RBrace => true; _ => false } }, "Expected '}'.")

  { name: name, params: params, return_type: return_type, body: body_expr }
}

/// 解析整个程序
fn Parser::parse_program(self: Self) -> Program {
  let funcs = []
  while !self.is_at_end() && self.peek() != Token::Eof {
    funcs.push(self.parse_func_def())
  }
  { funcs: funcs }
}

/// `parse` 是暴露给外部的主入口函数
pub fn parse(tokens: Array[Token]) -> Program {
  println("Debug: Parser - Starting parse, token count: " + tokens.length().to_string())
  let parser = Parser::new(tokens)
  let program = parser.parse_program()
  println("Debug: Parser - Finished parsing, found " + program.funcs.length().to_string() + " functions")
  program
}