﻿# MiniMoonBit 编译器评测系统集成脚本
# 使用方法：
# .\mmbc.ps1 --typecheck input.mbt
# .\mmbc.ps1 --codegen input.mbt

param(
    [Parameter(Mandatory=$true)]
    [string]$Mode,
    [Parameter(Mandatory=$true)]  
    [string]$InputFile
)

# 检查输入文件是否存在
if (!(Test-Path $InputFile)) {
    Write-Host "Error: Input file '$InputFile' not found" -ForegroundColor Red
    exit 1
}

# 检查模式参数
if ($Mode -ne "--typecheck" -and $Mode -ne "--codegen") {
    Write-Host "Error: Invalid mode '$Mode'. Use --typecheck or --codegen" -ForegroundColor Red
    exit 1
}

Write-Host "MiniMoonBit Compiler - Evaluation Mode" -ForegroundColor Green
Write-Host "Mode: $Mode" -ForegroundColor Cyan
Write-Host "Input: $InputFile" -ForegroundColor Cyan

try {
    # 直接调用 MoonBit 编译器
    $result = & moon run bin/main.mbt -- $Mode $InputFile 2>&1
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Host $result -ForegroundColor Green
    } else {
        Write-Host $result -ForegroundColor Red
    }
    
    exit $exitCode
}
catch {
    Write-Host "Error running compiler: $_" -ForegroundColor Red
    exit 1
}
